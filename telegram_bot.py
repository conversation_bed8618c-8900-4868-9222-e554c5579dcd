import requests
import json
import time
import logging
import os
import sys
import ssl
import urllib3
from datetime import datetime
from typing import Dict, Any

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class TelegramBot:
    def __init__(self, token: str):
        self.token = token
        self.base_url = f"https://api.telegram.org/bot{token}"

        self.setup_logging()

    def setup_logging(self):
        """设置日志记录"""
        # 创建logs目录
        if not os.path.exists('logs'):
            os.makedirs('logs')
        
        # 设置日志格式
        log_format = '%(asctime)s - %(levelname)s - %(message)s'
        
        # 配置文件日志
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.FileHandler('logs/bot.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def log_api_request(self, url: str, headers: dict, response: requests.Response, chat_id: str, elapsed_time: float = 0):
        """记录API请求的详细日志"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = f"logs/api_request_{timestamp}_{chat_id}.log"
        
        with open(log_filename, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write(f"API请求日志 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 80 + "\n\n")
            
            # 请求信息
            f.write("【请求信息】\n")
            f.write(f"URL: {url}\n")
            f.write(f"方法: GET\n")
            f.write(f"超时: 15秒\n\n")
            
            # 请求头
            f.write("【请求头】\n")
            for key, value in headers.items():
                f.write(f"{key}: {value}\n")
            f.write("\n")
            
            # 响应信息
            f.write("【响应信息】\n")
            f.write(f"状态码: {response.status_code}\n")
            f.write(f"状态描述: {response.reason}\n")
            f.write(f"响应大小: {len(response.content)} 字节\n")
            f.write(f"响应时间: {elapsed_time:.3f}秒\n\n")
            
            # 响应头
            f.write("【响应头】\n")
            for key, value in response.headers.items():
                f.write(f"{key}: {value}\n")
            f.write("\n")
            
            # 完整响应内容
            f.write("【完整响应内容】\n")
            f.write("-" * 50 + "\n")
            try:
                # 尝试格式化JSON
                if 'application/json' in response.headers.get('Content-Type', ''):
                    json_data = response.json()
                    f.write(json.dumps(json_data, indent=2, ensure_ascii=False))
                else:
                    f.write(response.text)
            except:
                f.write(response.text)
            f.write("\n" + "-" * 50 + "\n")
            
            # 原始字节数据（前1000字节）
            f.write("\n【原始字节数据（前1000字节）】\n")
            f.write(str(response.content[:1000]))
            f.write("\n\n")
            
            f.write("=" * 80 + "\n")
            f.write("日志结束\n")
            f.write("=" * 80 + "\n")
        
        return log_filename
    
    def send_message(self, chat_id: str, text: str, parse_mode: str = "HTML") -> Dict[str, Any]:
        """发送消息到指定聊天"""
        url = f"{self.base_url}/sendMessage"
        data = {
            "chat_id": chat_id,
            "text": text,
            "parse_mode": parse_mode
        }
        try:
            response = requests.post(url, json=data, verify=False, timeout=30)
            return response.json()
        except Exception as e:
            self.logger.error(f"发送消息失败: {str(e)}")
            return {"ok": False, "error": str(e)}

    # def check_token_expired(self, response_data: dict, api_name: str = "") -> bool:
    #     """检查API响应是否表示token过期"""
    #     if not isinstance(response_data, dict):
    #         return False

    #     # 检查状态码
    #     status_code = response_data.get('status_code')
    #     if status_code == 6001:
    #         return True

    #     # 检查消息内容
    #     message = response_data.get('message', '')
    #     if '30分钟内无任何操作' in message or '自动退出登录' in message or '登录已过期' in message:
    #         return True

    #     return False

    # def check_and_handle_token_expired(self, response_data: dict, chat_id: str, api_name: str = "", api_id: int = 0) -> bool:
    #     """检查token过期并处理，返回True表示已过期"""
    #     if self.check_token_expired(response_data, api_name):
    #         self.send_token_expired_message(chat_id, api_name, api_id)
    #         return True
    #     return False

    # def send_token_expired_message(self, chat_id: str, api_name: str = "", api_id: int = 0):
    #     """发送token过期提示消息"""
    #     expired_msg = f"🔐 <b>登录信息过期</b>\n"
    #     if api_name:
    #         expired_msg += f"平台：{api_name}\n"
    #     expired_msg += f"请重新登录后再试"

    #     self.send_message(chat_id, expired_msg)

    def get_updates(self, offset: int = None, timeout: int = 5) -> Dict[str, Any]:
        """获取消息更新"""
        url = f"{self.base_url}/getUpdates"
        params = {"timeout": timeout}  # 长轮询超时
        if offset:
            params["offset"] = offset

        print(f"🌐 发送请求到: {url}")
        print(f"📋 请求参数: {params}")
        sys.stdout.flush()

        try:
            # 使用长轮询，超时时间设置为timeout+5秒
            print(f"⏳ 等待响应... (超时: {timeout+5}秒)")
            sys.stdout.flush()

            # 添加更多的请求配置来解决SSL问题
            headers = {
                'User-Agent': 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Mobile Safari/537.36'
            }

            # 代理配置 - 如果你有代理服务器，请修改下面的配置
            proxies = {
                'http': 'http://127.0.0.1:7890',   # 修改为你的代理地址
                'https': 'http://127.0.0.1:7890'   # 修改为你的代理地址
            }

            # 尝试不使用代理
            try:
                response = requests.get(
                    url,
                    params=params,
                    headers=headers,
                    verify=False,
                    timeout=timeout+5,
                    allow_redirects=True
                )
            except:
                # 如果直连失败，尝试使用代理
                print("🔄 直连失败，尝试使用代理...")
                sys.stdout.flush()
                response = requests.get(
                    url,
                    params=params,
                    headers=headers,
                    verify=False,
                    timeout=timeout+5,
                    allow_redirects=True,
                    proxies=proxies
                )
            print(f"📡 响应状态码: {response.status_code}")
            print(f"📊 响应大小: {len(response.content)} 字节")
            sys.stdout.flush()

            if response.status_code == 200:
                result = response.json()
                print(f"✅ 成功获取更新: {len(result.get('result', []))} 条消息")
                sys.stdout.flush()
                return result
            else:
                self.logger.error(f"获取更新HTTP错误: {response.status_code}")
                return {"ok": False, "error": f"HTTP {response.status_code}"}
        except requests.exceptions.Timeout:
            # 超时是正常的，返回空结果继续轮询
            print("⏰ 请求超时，继续轮询...")
            sys.stdout.flush()
            return {"ok": True, "result": []}
        except requests.exceptions.ConnectionError as e:
            self.logger.error(f"连接错误: {str(e)}")
            print(f"❌ 连接错误: {str(e)}")
            sys.stdout.flush()
            return {"ok": False, "error": f"连接错误: {str(e)}"}
        except Exception as e:
            self.logger.error(f"获取更新失败: {str(e)}")
            print(f"💥 获取更新失败: {str(e)}")
            sys.stdout.flush()
            return {"ok": False, "error": str(e)}
    

    
    def _format_member_list(self, data: Dict[str, Any]) -> str:
        """格式化会员列表数据 - 仅返回头部信息，会员数据单独处理"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        
        # 提取基本信息
        status_code = data.get('status_code', 'N/A')
        message_text = data.get('message', 'N/A')
        
        # 提取分页信息
        data_info = data.get('data', {})
        page = data_info.get('page', 0)
        page_num = data_info.get('pageNum', 0)
        page_size = data_info.get('pageSize', 0)
        total = data_info.get('total', 0)
        
        # 构建消息头部
        message = f"👥 <b>当天注册会员列表</b> - {timestamp}\n\n"
        message += f"📈 <b>状态信息</b>\n"
        message += f"状态码: <code>{status_code}</code>\n"
        message += f"消息: <code>{message_text}</code>\n\n"
        
        message += f"📄 <b>分页信息</b>\n"
        message += f"当前页: <code>{page}</code>\n"
        message += f"页码: <code>{page_num}</code>\n"
        message += f"每页大小: <code>{page_size}</code>\n"
        message += f"总记录数: <code>{total}</code>\n\n"
        
        return message
    
    def _send_member_list_in_batches(self, chat_id: str, data: Dict[str, Any], api_name: str = ""):
        """分批发送会员数据 - 紧凑格式，在标题显示API名称"""

        # 处理会员列表
        data_info = data.get('data', {})
        if data_info is None:
            data_info = {}

        member_list = data_info.get('list', []) if isinstance(data_info, dict) else []
        if member_list is None:
            member_list = []

        if not member_list:
            # 获取总记录数
            total_count = data_info.get('total', 0) if isinstance(data_info, dict) else 0

            # 显示友好的提示信息
            no_data_msg = f"📊 <b>{api_name}</b>\n\n"
            no_data_msg += f"❌ 当天没有新注册会员（总记录数：{total_count}）\n\n"
            no_data_msg += f"💡 <b>提示：</b>\n"
            no_data_msg += f"• 查询日期：{datetime.now().strftime('%Y-%m-%d')}\n"
            no_data_msg += f"• API状态：{data.get('message', '正常')}\n"
            

            self.send_message(chat_id, no_data_msg)
            return

        # 智能分批策略：根据会员总数动态调整批次大小
        total_members = len(member_list)

        if total_members <= 20:
            batch_size = total_members  # 20个以内一次显示完
        elif total_members <= 50:
            batch_size = 25  # 21-50个，每批25个
        elif total_members <= 100:
            batch_size = 20  # 51-100个，每批20个
        else:
            batch_size = 15  # 超过100个，每批15个

        total_batches = (total_members + batch_size - 1) // batch_size

        for batch_num in range(total_batches):
            start_idx = batch_num * batch_size
            end_idx = min(start_idx + batch_size, total_members)
            batch_members = member_list[start_idx:end_idx]

            # 构建批次消息，在开头显示API名称
            api_prefix = f"{api_name} - " if api_name else ""
            batch_msg = f"👤 <b>{api_prefix}当天注册会员 (第{batch_num + 1}/{total_batches}批)</b>\n"
            batch_msg += f"📊 <b>显示第{start_idx + 1}-{end_idx}条，共{total_members}条</b>\n"
            batch_msg += "=" * 35 + "\n"

            for i, member in enumerate(batch_members):
                global_idx = start_idx + i + 1
                # 紧凑的两行格式，融合用户名和财务信息
                batch_msg += f"<b>#{global_idx} {member.get('name', 'N/A')}</b> | 💰 存款:<code>{member.get('deposit', '0')}</code> | 提款:<code>{member.get('draw', '0')}</code> | 盈利:<code>{member.get('profit', '0')}</code> | VIP:<code>{member.get('vipGradeStr', 'VIP0')}</code>\n"
                batch_msg += f"📅 注册:<code>{member.get('registerDate', 'N/A')}</code> | 登录:<code>{member.get('lastLoginTime', 'N/A')}</code> | 状态:<code>{'✅' if member.get('status') == '1' else '❌'}</code>\n"

                # 添加分隔线（除了批次中的最后一个）
                if i < len(batch_members) - 1:
                    batch_msg += "-" * 30 + "\n"

            # 发送批次消息
            self.send_message(chat_id, batch_msg)

            # 智能延迟：根据批次数量调整发送间隔
            if total_batches > 5:
                time.sleep(0.8)  # 批次多时稍微慢一点
            elif total_batches > 2:
                time.sleep(0.5)  # 中等批次正常速度
            else:
                time.sleep(0.2)  # 批次少时快速发送

        # 不再发送总结消息，保持简洁
    
    def get_api_data_by_id(self, id: int) -> dict:
        """根据id从JSON文件获取对应的API数据"""
        import json
        headers_file = 'api_headers.json'
        
        if os.path.exists(headers_file):
            try:
                with open(headers_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                    if isinstance(data, list):
                        for item in data:
                            # 处理ID可能是字符串或数字的情况
                            item_id = int(item.get("id", 0)) if isinstance(item.get("id"), (str, int)) else 0
                            if item_id == id:
                                return item
                    else:
                        # 如果是单个对象且ID匹配
                        item_id = int(data.get("id", 0)) if isinstance(data.get("id"), (str, int)) else 0
                        if item_id == id:
                            return data
                            
            except Exception as e:
                self.logger.error(f"从JSON文件加载API数据失败: {str(e)}")
        
        # 如果JSON文件不存在或读取失败，使用硬编码的备用数据
        fallback_data = [
            {"id":1,"username":"开云","url":"https://www.tjhn65.com:9174/","X-API-TOKEN":"DL_e70d4d3853817fd1d76e5b1bcac5c5eb","X-API-UUID":"459AFAAA-F783-4337-8632-81B5469A0DD3","X-API-XXX":"932eba939bdce567f96089dcdb2dcc581f7069afdd6812e1fccf3f0bbcbe7732","Cookie":"X-API-UUID=3217df7c-6920-44cc-9adc-86a6ff638036; X-API-TOKEN=DL_e70d4d3853817fd1d76e5b1bcac5c5eb"},
            {"id":2,"username":"乐鱼","url":"https://www.x3w8in.vip:8005/","X-API-TOKEN":"DL_0a3c817df2f31d821d8a17f58b629fb0","X-API-UUID":"B8CF779A-67DA-4E22-AF14-374869A287E3","X-API-XXX":"3391e64602271eab2284c716da48067be6e61d722fa87b503b4a96d9538775a3","Cookie":"X-API-UUID=582251d9-4719-4cef-934e-5dfa63fb1745; X-API-TOKEN=DL_0a3c817df2f31d821d8a17f58b629fb0"},
            {"id":3,"username":"开云","url":"https://www.tjhn65.com:9174/","X-API-TOKEN":"DL_e70d4d3853817fd1d76e5b1bcac5c5eb","X-API-UUID":"459AFAAA-F783-4337-8632-81B5469A0DD3","X-API-XXX":"932eba939bdce567f96089dcdb2dcc581f7069afdd6812e1fccf3f0bbcbe7732","Cookie":"X-API-UUID=3217df7c-6920-44cc-9adc-86a6ff638036; X-API-TOKEN=DL_e70d4d3853817fd1d76e5b1bcac5c5eb"},
            {"id":4,"username":"开云","url":"https://www.tjhn65.com:9174/","X-API-TOKEN":"DL_e70d4d3853817fd1d76e5b1bcac5c5eb","X-API-UUID":"459AFAAA-F783-4337-8632-81B5469A0DD3","X-API-XXX":"932eba939bdce567f96089dcdb2dcc581f7069afdd6812e1fccf3f0bbcbe7732","Cookie":"X-API-UUID=3217df7c-6920-44cc-9adc-86a6ff638036; X-API-TOKEN=DL_e70d4d3853817fd1d76e5b1bcac5c5eb"}
        ]
        
        for item in fallback_data:
            if item["id"] == id:
                return item
        
        return {}

    def fetch_member_data_post_by_id(self, chat_id: str, id: int, page_num: int = 1, page_size: int = 100):
        """根据id使用POST请求获取会员数据"""
        api_data = self.get_api_data_by_id(id)
        if not api_data:
            self.send_message(chat_id, f"❌ 未找到ID为{id}的API数据")
            return

        # 获取API名称用于显示
        api_username = api_data.get('username', f'API-{id}')

        headers = {
            "X-API-TOKEN": api_data.get("X-API-TOKEN", ""),
            "X-API-UUID": api_data.get("X-API-UUID", ""),
            "X-API-XXX": api_data.get("X-API-XXX", ""),
            "Cookie": api_data.get("Cookie", ""),
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json, text/plain, */*",
            "Content-Type": "application/json"
        }
        api_base_url = api_data.get("url", "https://www.tjhn65.com:9174").rstrip('/')

        from datetime import datetime
        today = datetime.now()

        payload = {
            "pageNum": page_num,
            "pageSize": page_size,
            "registerSort": -1,
            "drawSort": -1,
            "depositSort": -1,
            "lastLoginTimeSort": -1,
            "endDate": today.strftime("%Y-%m-%d"),
            "firstPayEndTime": "",
            "firstPayStartTime": "",
            "isBet": "",
            "isRest": False,
            "maxPay": None,
            "minPay": None,
            "name": "",
            "registerEndDate": today.strftime("%Y-%m-%d"),  # 当天注册结束时间
            "registerStartDate": today.strftime("%Y-%m-%d"),  # 当天注册开始时间
            "startDate": today.strftime("%Y-%m-%d"),
            "status": -1,
            "tagsFlag": "1",
            "tagsIds": []
        }

        api_url = f"{api_base_url}/agent/api/v1/member/list"

        try:
            # 后台执行请求，不发送中间状态消息
            start_time = time.time()
            response = requests.post(api_url, headers=headers, json=payload, timeout=15, verify=False)
            elapsed_time = time.time() - start_time

            # 记录日志到文件（后台）
            self.log_api_request_post(api_url, headers, payload, response, str(chat_id), elapsed_time)
            self.logger.info(f"API请求完成 - ID:{id}, 状态码:{response.status_code}, 耗时:{elapsed_time:.2f}秒")

            if response.status_code == 200:
                try:
                    json_data = response.json()

                    # 添加调试日志
                    self.logger.info(f"API {id} 响应数据: {json_data}")

                    # 静默处理，不发送成功消息，直接显示数据

                    # 使用分批发送功能显示所有会员数据，传入API名称
                    self._send_member_list_in_batches(chat_id, json_data, api_username)

                except json.JSONDecodeError:
                    error_msg = f"\u26a0\ufe0f JSON解析失败\n原始响应: <pre>{response.text[:1000]}</pre>"
                    self.send_message(chat_id, error_msg)

            else:
                error_msg = f"❌ <b>请求失败 (ID: {id})</b>\n"
                error_msg += f"状态码: <code>{response.status_code}</code>\n"
                error_msg += f"错误: {response.text[:200]}"
                self.send_message(chat_id, error_msg)

        except Exception as e:
            error_msg = f"💥 <b>请求异常 (ID: {id})</b>\n错误: {str(e)}"
            self.send_message(chat_id, error_msg)
            self.logger.error(f"API请求异常 - ID:{id}, 错误:{str(e)}")



    def fetch_all_summary_data(self, chat_id: str):
        """获取所有API的综合数据"""
        # 获取所有API配置
        import json
        headers_file = 'api_headers.json'

        if not os.path.exists(headers_file):
            self.send_message(chat_id, "❌ API配置文件不存在")
            return

        try:
            with open(headers_file, 'r', encoding='utf-8') as f:
                api_configs = json.load(f)

            if not isinstance(api_configs, list):
                api_configs = [api_configs]

            from datetime import datetime
            today_str = datetime.now().strftime("%Y-%m-%d")

            # 发送开始消息
            start_msg = f"📊 <b>获取所有平台 {today_str} 综合数据</b>\n正在处理..."
            self.send_message(chat_id, start_msg)

            # 逐个获取每个API的数据
            for api_config in api_configs:
                api_id = api_config.get('id', 0)
                if api_id:
                    # 使用综合数据获取方法
                    self.fetch_summary_data_by_id(chat_id, int(api_id))
                    time.sleep(1)  # 避免请求过快

        except Exception as e:
            error_msg = f"💥 <b>获取所有综合数据失败</b>\n错误: {str(e)}"
            self.send_message(chat_id, error_msg)
            self.logger.error(f"获取所有综合数据异常 - 错误:{str(e)}")

    def fetch_summary_data_by_id(self, chat_id: str, id: int):
        """根据id获取组合数据（会员数据 + 财务数据）"""
        api_data = self.get_api_data_by_id(id)
        if not api_data:
            self.send_message(chat_id, f"❌ 未找到ID为{id}的API数据")
            return

        api_username = api_data.get('username', f'API-{id}')

        # 获取会员数据（注册数据）
        member_data = self._get_member_data_by_id(chat_id, id)

        # 获取活跃会员数据
        active_data = self.fetch_active_member_data_by_id(chat_id, id)

        # 获取财务数据
        finance_data = self.fetch_finance_data_by_id(chat_id, id)

        # 组合并发送数据（即使有数据为None也继续处理）
        self._send_combined_data(chat_id, member_data, finance_data, active_data, api_username, id)

    def _send_summary_data(self, chat_id: str, data: Dict[str, Any], api_name: str = "", api_id: int = 0):
        """发送综合数据摘要"""
        # 处理数据
        data_info = data.get('data', {})
        if data_info is None:
            data_info = {}

        member_list = data_info.get('list', []) if isinstance(data_info, dict) else []
        if member_list is None:
            member_list = []

        total_count = data_info.get('total', 0) if isinstance(data_info, dict) else 0

        # 计算统计数据
        total_deposit = 0
        total_draw = 0
        total_profit = 0
        active_members = 0

        for member in member_list:
            if isinstance(member, dict):
                total_deposit += float(member.get('deposit', 0) or 0)
                total_draw += float(member.get('draw', 0) or 0)
                total_profit += float(member.get('profit', 0) or 0)
                if member.get('status') == '1':
                    active_members += 1

        # 格式化综合数据消息（紧凑格式）
        summary_msg = f"📊 <b>{api_name}</b>\n"
        summary_msg += f"👥 总会员: <code>{total_count}</code> | 活跃: <code>{active_members}</code> | 今日: <code>{len(member_list)}</code>\n"

        if total_count == 0:
            summary_msg += f"❌ 当天没有数据记录"

        self.send_message(chat_id, summary_msg)

    def fetch_finance_data_by_id(self, chat_id: str, id: int):
        """获取指定ID的财务数据"""
        api_data = self.get_api_data_by_id(id)
        if not api_data:
            return None

        headers = {
            "X-API-TOKEN": api_data.get("X-API-TOKEN", ""),
            "X-API-UUID": api_data.get("X-API-UUID", ""),
            "X-API-XXX": api_data.get("X-API-XXX", ""),
            "Cookie": api_data.get("Cookie", ""),
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json, text/plain, */*",
            "Content-Type": "application/json"
        }
        api_base_url = api_data.get("url", "https://www.tjhn65.com:9174").rstrip('/')

        from datetime import datetime
        today = datetime.now()

        # 财务数据的载荷
        payload = {
            "startDate": today.strftime("%Y-%m-%d"),
            "endDate": today.strftime("%Y-%m-%d"),
            "topId": 0
        }

        # 财务数据的API端点
        api_url = f"{api_base_url}/agent/api/v1/finance/excel/total"

        try:
            start_time = time.time()
            response = requests.post(api_url, headers=headers, json=payload, timeout=15, verify=False)
            elapsed_time = time.time() - start_time

            # 记录日志
            self.log_api_request_post(api_url, headers, payload, response, str(chat_id), elapsed_time)
            self.logger.info(f"财务数据请求完成 - ID:{id}, 状态码:{response.status_code}, 耗时:{elapsed_time:.2f}秒")

            if response.status_code == 200:
                try:
                    json_data = response.json()
                    self.logger.info(f"财务数据 API {id} 响应数据: {json_data}")
                    return json_data
                except json.JSONDecodeError:
                    self.logger.error(f"财务数据JSON解析失败 - ID:{id}")
                    return None
            else:
                self.logger.error(f"财务数据请求失败 - ID:{id}, 状态码:{response.status_code}")
                return None

        except Exception as e:
            self.logger.error(f"财务数据请求异常 - ID:{id}, 错误:{str(e)}")
            return None

    def fetch_combined_data_by_id(self, chat_id: str, id: int):
        """获取组合数据：会员数据 + 财务数据"""
        api_data = self.get_api_data_by_id(id)
        if not api_data:
            self.send_message(chat_id, f"❌ 未找到ID为{id}的API数据")
            return

        api_username = api_data.get('username', f'API-{id}')

        # 获取会员数据
        member_data = self._get_member_data_by_id(chat_id, id)

        # 获取财务数据
        finance_data = self.fetch_finance_data_by_id(chat_id, id)

        # 组合并发送数据
        self._send_combined_data(chat_id, member_data, finance_data, api_username, id)

    def _get_member_data_by_id(self, chat_id: str, id: int):
        """内部方法：获取会员数据（不发送消息）"""
        api_data = self.get_api_data_by_id(id)
        if not api_data:
            return None

        headers = {
            "X-API-TOKEN": api_data.get("X-API-TOKEN", ""),
            "X-API-UUID": api_data.get("X-API-UUID", ""),
            "X-API-XXX": api_data.get("X-API-XXX", ""),
            "Cookie": api_data.get("Cookie", ""),
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json, text/plain, */*",
            "Content-Type": "application/json"
        }
        api_base_url = api_data.get("url", "https://www.tjhn65.com:9174").rstrip('/')

        from datetime import datetime
        today = datetime.now()

        payload = {
            "pageNum": 1,
            "pageSize": 100,
            "registerSort": -1,
            "drawSort": -1,
            "depositSort": -1,
            "lastLoginTimeSort": -1,
            "endDate": today.strftime("%Y-%m-%d"),
            "firstPayEndTime": "",
            "firstPayStartTime": "",
            "isBet": "",
            "isRest": False,
            "maxPay": None,
            "minPay": None,
            "name": "",
            "registerEndDate": today.strftime("%Y-%m-%d"),
            "registerStartDate": today.strftime("%Y-%m-%d"),
            "startDate": today.strftime("%Y-%m-%d"),
            "status": -1,
            "tagsFlag": "1",
            "tagsIds": []
        }

        api_url = f"{api_base_url}/agent/api/v1/member/list"

        try:
            start_time = time.time()
            response = requests.post(api_url, headers=headers, json=payload, timeout=15, verify=False)
            elapsed_time = time.time() - start_time

            if response.status_code == 200:
                try:
                    json_data = response.json()
                    return json_data
                except json.JSONDecodeError:
                    return None
            else:
                return None

        except Exception as e:
            return None

    def fetch_active_member_data_by_id(self, chat_id: str, id: int):
        """获取指定ID的活跃会员数据"""
        api_data = self.get_api_data_by_id(id)
        if not api_data:
            return None

        headers = {
            "X-API-TOKEN": api_data.get("X-API-TOKEN", ""),
            "X-API-UUID": api_data.get("X-API-UUID", ""),
            "X-API-XXX": api_data.get("X-API-XXX", ""),
            "Cookie": api_data.get("Cookie", ""),
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json, text/plain, */*",
            "Content-Type": "application/json"
        }
        api_base_url = api_data.get("url", "https://www.tjhn65.com:9174").rstrip('/')

        from datetime import datetime
        today = datetime.now()

        # 活跃会员的载荷
        payload = {
            "pageNum": 1,
            "pageSize": 10,
            "registerSort": -1,
            "drawSort": -1,
            "depositSort": -1,
            "lastLoginTimeSort": -1,
            "endDate": today.strftime("%Y-%m-%d"),
            "firstPayEndTime": "",
            "firstPayStartTime": "",
            "isBet": "1",  # 关键参数：查询有投注的会员
            "isRest": False,
            "maxPay": None,
            "minPay": None,
            "name": "",
            "registerEndDate": "",
            "registerStartDate": "",
            "startDate": today.strftime("%Y-%m-%d"),
            "status": -1,
            "tagsFlag": "1",
            "tagsIds": []
        }

        # 活跃会员的API端点
        api_url = f"{api_base_url}/agent/api/v1/member/list"

        try:
            start_time = time.time()
            response = requests.post(api_url, headers=headers, json=payload, timeout=15, verify=False)
            elapsed_time = time.time() - start_time

            # 记录日志
            self.log_api_request_post(api_url, headers, payload, response, str(chat_id), elapsed_time)
            self.logger.info(f"活跃会员数据请求完成 - ID:{id}, 状态码:{response.status_code}, 耗时:{elapsed_time:.2f}秒")

            if response.status_code == 200:
                try:
                    json_data = response.json()
                    self.logger.info(f"活跃会员数据 API {id} 响应数据: {json_data}")
                    return json_data
                except json.JSONDecodeError:
                    self.logger.error(f"活跃会员数据JSON解析失败 - ID:{id}")
                    return None
            else:
                self.logger.error(f"活跃会员数据请求失败 - ID:{id}, 状态码:{response.status_code}")
                return None

        except Exception as e:
            self.logger.error(f"活跃会员数据请求异常 - ID:{id}, 错误:{str(e)}")
            return None



    def _send_combined_data(self, chat_id: str, member_data: Dict[str, Any], finance_data: Dict[str, Any], active_data: Dict[str, Any], api_name: str = "", api_id: int = 0):
        """发送组合数据（会员数据 + 财务数据 + 活跃数据）"""
        from datetime import datetime
        today_str = datetime.now().strftime("%Y-%m-%d")

        # 处理会员数据（注册数据）
        member_info = member_data.get('data', {}) if member_data else {}
        if member_info is None:
            member_info = {}

        member_list = member_info.get('list', []) if isinstance(member_info, dict) else []
        if member_list is None:
            member_list = []

        # 计算会员统计
        register_count = len(member_list)  # 注册人数（当天）
        first_deposit_count = 0  # 首存人数

        for member in member_list:
            if isinstance(member, dict):
                # 首存人数（有存款记录的会员）
                if float(member.get('deposit', 0) or 0) > 0:
                    first_deposit_count += 1

        # 处理活跃会员数据
        active_info = active_data.get('data', {}) if active_data else {}
        if active_info is None:
            active_info = {}

        # 活跃人数从 total 字段获取
        active_count = active_info.get('total', 0) if isinstance(active_info, dict) else 0

        # 处理财务数据
        finance_info = finance_data.get('data', {}) if finance_data else {}
        if finance_info is None:
            finance_info = {}

        # 根据你提供的字段映射
        deposit_amount = float(finance_info.get('deposit', 0) or 0)  # 存款金额
        bet_amount = float(finance_info.get('betAmount', 0) or 0)  # 投注金额
        profit_amount = float(finance_info.get('profit', 0) or 0)  # 输赢金额

        # 格式化组合数据消息
        combined_msg = f"✅（{api_name}）{today_str}数据\n"
        combined_msg += f"👥注册人数：{register_count}\n"
        combined_msg += f"💰首存人数：{first_deposit_count}\n"
        combined_msg += f"🔥活跃人数：{active_count}\n"
        combined_msg += f"🏦存款金额：{deposit_amount}\n"
        combined_msg += f"🔄投注金额：{bet_amount}\n"
        combined_msg += f"🎯输赢金额：{profit_amount}"

        self.send_message(chat_id, combined_msg)

    def load_api_headers(self, api_id: int = 1):
        """从JSON文件加载API请求头和基础URL"""
        import json
        headers_file = 'api_headers.json'
        if os.path.exists(headers_file):
            try:
                with open(headers_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                    # 如果是数组格式，根据ID选择对应的数据
                    if isinstance(data, list):
                        api_data = None
                        for item in data:
                            # 处理ID可能是字符串或数字的情况
                            item_id = int(item.get("id", 0)) if isinstance(item.get("id"), (str, int)) else 0
                            if item_id == api_id:
                                api_data = item
                                break
                        
                        if not api_data:
                            # 如果找不到指定ID，使用第一个
                            api_data = data[0] if data else {}
                    else:
                        # 如果是单个对象格式，直接使用
                        api_data = data
                    
                    headers = {
                        "X-API-TOKEN": api_data.get("X-API-TOKEN", ""),
                        "X-API-UUID": api_data.get("X-API-UUID", ""),
                        "X-API-XXX": api_data.get("X-API-XXX", ""),
                        "Cookie": api_data.get("Cookie", ""),
                        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                        "Accept": "application/json, text/plain, */*",
                        "Content-Type": "application/json"
                    }
                    api_base_url = api_data.get("url", "https://www.tjhn65.com:9174").rstrip('/')
                    api_username = api_data.get("username", f"API-{api_id}")
                    return headers, api_base_url, api_username
                    
            except Exception as e:
                self.logger.error(f"加载API请求头失败: {str(e)}")
        
        # 默认空头、默认URL和默认用户名
        return {
            "X-API-TOKEN": "",
            "X-API-UUID": "",
            "X-API-XXX": "",
            "Cookie": "",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json, text/plain, */*",
            "Content-Type": "application/json"
        }, "https://www.tjhn65.com:9174", f"API-{api_id}"

        
        # API载荷数据 - 只修改日期为当天
        from datetime import datetime
        today = datetime.now()
        
        payload = {
            "pageNum": 1,
            "pageSize": 100,
            "registerSort": -1,
            "drawSort": -1,
            "depositSort": -1,
            "lastLoginTimeSort": -1,
            "endDate": today.strftime("%Y-%m-%d"),
            "firstPayEndTime": "",
            "firstPayStartTime": "",
            "isBet": "",
            "isRest": False,
            "maxPay": None,
            "minPay": None,
            "name": "",
            "registerEndDate": today.strftime("%Y-%m-%d"),  # 当天注册结束时间
            "registerStartDate": today.strftime("%Y-%m-%d"),  # 当天注册开始时间
            "startDate": today.strftime("%Y-%m-%d"),
            "status": -1,
            "tagsFlag": "1",
            "tagsIds": []
        }
        
        api_url = "https://www.tjhn65.com:9174/agent/api/v1/member/list"
        
        try:
            self.send_message(chat_id, "📊 <b>POST请求获取会员数据</b>\n正在发送请求...")
            
            # 记录开始时间
            start_time = time.time()
            
            # 发送POST请求
            response = requests.post(api_url, headers=headers, json=payload, timeout=15, verify=False)
            
            # 计算请求耗时
            elapsed_time = time.time() - start_time
            
            # 记录详细日志
            self.log_api_request_post(api_url, headers, payload, response, str(chat_id), elapsed_time)
            
            # 发送状态信息
            status_msg = f"📊 <b>POST请求结果</b>\n"
            status_msg += f"状态码: <code>{response.status_code}</code>\n"
            status_msg += f"响应时间: <code>{elapsed_time:.2f}秒</code>\n"
            status_msg += f"响应大小: <code>{len(response.content)}字节</code>\n"
            status_msg += f"Content-Type: <code>{response.headers.get('Content-Type', '未知')}</code>\n"
            status_msg += f"日志文件: <code>{log_filename}</code>\n\n"
            
            if response.status_code == 200:
                status_msg += "✅ 请求成功！"
                self.send_message(chat_id, status_msg)
                
                try:
                    # 解析JSON数据
                    json_data = response.json()
                    
                    # 发送数据信息
                    info_msg = f"🎉 <b>成功获取会员数据！</b>\n"
                    if isinstance(json_data, dict):
                        info_msg += f"根级键: {', '.join(list(json_data.keys())[:5])}\n"
                        if 'data' in json_data and 'total' in json_data['data']:
                            info_msg += f"总会员数: <code>{json_data['data']['total']}</code>\n"
                    self.send_message(chat_id, info_msg)
                    
                    # 使用分批发送功能显示所有会员数据，传入API名称
                    self._send_member_list_in_batches(chat_id, json_data, api_username)
                    
                except json.JSONDecodeError:
                    error_msg = f"⚠️ JSON解析失败\n原始响应: <pre>{response.text[:1000]}</pre>"
                    self.send_message(chat_id, error_msg)
                    
            else:
                status_msg += f"❌ 请求失败: {response.text[:200]}"
                self.send_message(chat_id, status_msg)
                
        except Exception as e:
            error_msg = f"💥 <b>POST请求失败</b>\n错误: {str(e)}"
            self.send_message(chat_id, error_msg)
    
    def log_api_request_post(self, url: str, headers: dict, payload: dict, response: requests.Response, chat_id: str, elapsed_time: float = 0):
        """记录POST API请求的详细日志到固定文件"""
        # 使用固定的日志文件名
        log_filename = "logs/api_requests.log"

        # 确保logs目录存在
        os.makedirs('logs', exist_ok=True)

        # 追加模式写入日志
        with open(log_filename, 'a', encoding='utf-8') as f:
            f.write("\n" + "=" * 80 + "\n")
            f.write(f"POST API请求日志 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - Chat: {chat_id}\n")
            f.write("=" * 80 + "\n")

            # 请求信息
            f.write("【请求信息】\n")
            f.write(f"URL: {url}\n")
            f.write(f"方法: POST\n")
            f.write(f"超时: 15秒\n")
            f.write(f"响应时间: {elapsed_time:.3f}秒\n")
            f.write(f"状态码: {response.status_code}\n\n")

            # 请求载荷（简化版）
            f.write("【请求载荷】\n")
            try:
                # 只记录关键信息，减少日志大小
                key_info = {
                    'pageNum': payload.get('pageNum'),
                    'pageSize': payload.get('pageSize'),
                    'startDate': payload.get('startDate'),
                    'endDate': payload.get('endDate'),
                    'registerStartDate': payload.get('registerStartDate'),
                    'registerEndDate': payload.get('registerEndDate')
                }
                f.write(json.dumps(key_info, indent=2, ensure_ascii=False))
            except:
                f.write(str(payload))
            f.write("\n\n")

            # 响应内容（简化版）
            f.write("【响应结果】\n")
            try:
                if response.status_code == 200:
                    json_data = response.json()
                    # 只记录关键响应信息
                    if 'status_code' in json_data:
                        f.write(f"API状态码: {json_data.get('status_code')}\n")
                        f.write(f"API消息: {json_data.get('message', 'N/A')}\n")

                        if 'data' in json_data and isinstance(json_data['data'], dict):
                            data = json_data['data']
                            if 'total' in data:
                                f.write(f"总记录数: {data.get('total')}\n")
                            if 'list' in data:
                                list_data = data.get('list', [])
                                if list_data is None:
                                    list_data = []
                                f.write(f"返回记录数: {len(list_data)}\n")
                    else:
                        f.write(f"响应内容: {str(json_data)[:200]}...\n")
                else:
                    f.write(f"请求失败: {response.status_code} - {response.reason}\n")
                    f.write(f"错误内容: {response.text[:200]}...\n")
            except Exception as e:
                f.write(f"解析响应失败: {str(e)}\n")

            f.write("-" * 80 + "\n")

        return log_filename

    def fetch_member_data_with_session(self, chat_id: str):
        """智能两步获取会员数据：使用Session保持状态"""
        # 使用requests.Session来自动管理Cookie
        session = requests.Session()
        
        headers, api_base_url, api_username = self.load_api_headers()
        # 设置Session的默认头部
        session.headers.update(headers)
        
        try:
            # 第一步：访问 /app/subordinate/member 建立会话
            session_url = f"{api_base_url}/app/subordinate/member"
            
            self.send_message(chat_id, "🔐 <b>步骤1/2</b> - 建立会话连接...")
            
            session_start = time.time()
            session_response = session.get(session_url, timeout=15, verify=False)
            session_elapsed = time.time() - session_start
            
            # 记录第一步的日志
            session_log = self.log_api_request(session_url, dict(session.headers), session_response, f"{chat_id}_session", session_elapsed)
            
            # 分析第一步响应内容
            session_analysis = f"🔍 <b>会话分析</b>\n"
            session_analysis += f"状态码: <code>{session_response.status_code}</code>\n"
            session_analysis += f"响应时间: <code>{session_elapsed:.2f}秒</code>\n"
            session_analysis += f"响应大小: <code>{len(session_response.content)}字节</code>\n"
            session_analysis += f"Content-Type: <code>{session_response.headers.get('Content-Type', '未知')}</code>\n"
            
            # 检查Cookie变化
            if session.cookies:
                session_analysis += f"🍪 Session Cookies: <code>{len(session.cookies)}</code>个\n"
                for cookie in session.cookies:
                    session_analysis += f"  • {cookie.name}={cookie.value[:20]}...\n"
            
            # 检查响应头中的重要信息
            important_headers = ['Set-Cookie', 'Location', 'X-CSRF-Token', 'Authorization']
            for header in important_headers:
                if header in session_response.headers:
                    session_analysis += f"📋 {header}: <code>{session_response.headers[header][:100]}...</code>\n"
            
            session_analysis += f"\n📝 日志文件: <code>{session_log}</code>\n\n"
            
            if session_response.status_code == 200:
                session_analysis += "✅ 会话建立成功，分析响应内容..."
                self.send_message(chat_id, session_analysis)
                
                # 分析HTML内容，寻找可能的API端点或参数
                html_content = session_response.text
                
                # 寻找可能的API相关信息
                api_hints = []
                if '/agent/api/' in html_content:
                    api_hints.append("发现 /agent/api/ 路径引用")
                if 'member' in html_content.lower():
                    api_hints.append("发现 member 相关内容")
                if 'token' in html_content.lower():
                    api_hints.append("发现 token 相关内容")
                if 'csrf' in html_content.lower():
                    api_hints.append("发现 CSRF 相关内容")
                
                if api_hints:
                    hints_msg = "🔍 <b>HTML内容分析</b>\n" + "\n".join([f"• {hint}" for hint in api_hints])
                    self.send_message(chat_id, hints_msg)
                
                # 第二步：尝试多个可能的API端点
                possible_endpoints = [
                    "/agent/api/v1/member/list",
                   
                ]
                
                self.send_message(chat_id, "📊 <b>步骤2/2</b> - 尝试多个API端点...")
                
                for i, endpoint in enumerate(possible_endpoints):
                    data_url = f"{api_base_url}{endpoint}"
                    
                    self.send_message(chat_id, f"🔄 尝试端点 {i+1}/{len(possible_endpoints)}: <code>{endpoint}</code>")
                    
                    # 更新请求头为API请求
                    api_headers = {
                        "Accept": "application/json, text/plain, */*",
                        "Content-Type": "application/json",
                        "X-Requested-With": "XMLHttpRequest"
                    }
                    session.headers.update(api_headers)
                    
                    data_start = time.time()
                    data_response = session.get(data_url, timeout=15, verify=False)
                    data_elapsed = time.time() - data_start
                    
                    # 记录每次尝试的日志
                    data_log = self.log_api_request(data_url, dict(session.headers), data_response, f"{chat_id}_data_{i+1}", data_elapsed)
                    
                    result_msg = f"📊 <b>端点 {i+1} 结果</b>\n"
                    result_msg += f"URL: <code>{endpoint}</code>\n"
                    result_msg += f"状态码: <code>{data_response.status_code}</code>\n"
                    result_msg += f"响应时间: <code>{data_elapsed:.2f}秒</code>\n"
                    result_msg += f"响应大小: <code>{len(data_response.content)}字节</code>\n"
                    result_msg += f"Content-Type: <code>{data_response.headers.get('Content-Type', '未知')}</code>\n"
                    
                    if data_response.status_code == 200:
                        result_msg += "✅ 请求成功！"
                        self.send_message(chat_id, result_msg)
                        
                        try:
                            # 尝试解析JSON数据
                            json_data = data_response.json()
                            
                            # 发送数据信息
                            info_msg = f"🎉 <b>成功获取数据！</b>\n"
                            info_msg += f"端点: <code>{endpoint}</code>\n"
                            info_msg += f"数据类型: JSON\n"
                            if isinstance(json_data, dict):
                                info_msg += f"根级键: {', '.join(list(json_data.keys())[:5])}\n"
                            info_msg += f"日志文件: <code>{data_log}</code>"
                            self.send_message(chat_id, info_msg)
                            
                            # 格式化并发送数据
                            formatted_data = self._format_api_response(json_data)
                            self.send_message(chat_id, formatted_data)
                            
                            # 成功获取数据，退出循环
                            return
                            
                        except json.JSONDecodeError:
                            # 如果不是JSON但状态码是200，可能是HTML页面
                            if 'text/html' in data_response.headers.get('Content-Type', ''):
                                result_msg += "\n⚠️ 返回HTML页面，可能需要登录"
                            else:
                                result_msg += f"\n📄 文本响应: {data_response.text[:100]}..."
                            
                    elif data_response.status_code == 404:
                        result_msg += "❌ 端点不存在"
                    elif data_response.status_code == 403:
                        result_msg += "🔒 权限不足"
                    elif data_response.status_code == 401:
                        result_msg += "🔐 需要认证"
                    else:
                        result_msg += f"❌ 请求失败: {data_response.text[:100]}"
                    
                    self.send_message(chat_id, result_msg)
                
                # 如果所有端点都失败
                self.send_message(chat_id, "❌ <b>所有API端点都无法访问</b>\n可能需要不同的认证方式或参数")
                    
            else:
                session_analysis += f"❌ 会话建立失败: {session_response.text[:200]}"
                self.send_message(chat_id, session_analysis)
                
        except Exception as e:
            error_msg = f"💥 <b>智能请求失败</b>\n"
            error_msg += f"错误: {str(e)}"
            self.send_message(chat_id, error_msg)
        finally:
            # 关闭session
            session.close()

    def fetch_api_data_once(self, chat_id: str, endpoint: str = ""):
        """单次获取API数据"""
        headers, api_base_url, api_username = self.load_api_headers()
        
        try:
            api_url = f"{api_base_url}/{endpoint}" if endpoint else f"{api_base_url}/agent/api/v1/member/list"
            
            # 发送详细的调试信息
            debug_msg = f"🔍 <b>调试信息</b>\n"
            debug_msg += f"<b>请求URL:</b> <code>{api_url}</code>\n"
            debug_msg += f"<b>请求头数量:</b> {len(headers)}\n"
            debug_msg += f"<b>超时设置:</b> 15秒\n\n"
            debug_msg += f"🔄 开始请求..."
            self.send_message(chat_id, debug_msg)
            
            # 记录开始时间
            start_time = time.time()
            
            response = requests.get(api_url, headers=headers, timeout=15, verify=False)
            
            # 计算请求耗时
            elapsed_time = time.time() - start_time
            
            # 记录详细日志到文件
            log_filename = self.log_api_request(api_url, headers, response, str(chat_id), elapsed_time)
            self.logger.info(f"API请求日志已保存到: {log_filename}")
            
            # 发送日志文件路径给用户
            log_msg = f"📝 <b>详细日志已生成</b>\n"
            log_msg += f"<b>文件路径:</b> <code>{log_filename}</code>\n"
            log_msg += f"<b>请求耗时:</b> {elapsed_time:.2f}秒"
            self.send_message(chat_id, log_msg)
            
            # 发送响应状态信息
            status_msg = f"📊 <b>响应状态</b>\n"
            status_msg += f"<b>状态码:</b> {response.status_code}\n"
            status_msg += f"<b>响应时间:</b> {elapsed_time:.2f}秒\n"
            status_msg += f"<b>响应大小:</b> {len(response.content)}字节\n"
            status_msg += f"<b>Content-Type:</b> {response.headers.get('Content-Type', '未知')}\n\n"
            
            if response.status_code == 200:
                status_msg += "✅ 请求成功，正在处理数据..."
                self.send_message(chat_id, status_msg)
                
                try:
                    # 尝试解析JSON
                    data = response.json()
                    
                    # 发送数据类型信息
                    data_info = f"📋 <b>数据信息</b>\n"
                    data_info += f"<b>数据类型:</b> JSON\n"
                    data_info += f"<b>根级键数量:</b> {len(data) if isinstance(data, dict) else '非字典类型'}\n"
                    if isinstance(data, dict):
                        data_info += f"<b>主要键:</b> {', '.join(list(data.keys())[:5])}\n"
                    elif isinstance(data, list):
                        data_info += f"<b>数组长度:</b> {len(data)}\n"
                    self.send_message(chat_id, data_info)
                    
                    # 格式化并发送数据
                    formatted_data = self._format_api_response(data)
                    self.send_message(chat_id, formatted_data)
                    
                except json.JSONDecodeError as json_err:
                    # JSON解析失败
                    json_error_msg = f"⚠️ <b>JSON解析失败</b>\n"
                    json_error_msg += f"<b>错误:</b> {str(json_err)}\n"
                    json_error_msg += f"<b>原始响应前500字符:</b>\n"
                    json_error_msg += f"<pre>{response.text[:500]}</pre>"
                    self.send_message(chat_id, json_error_msg)
                    
                    # 如果不是JSON，直接显示文本
                    if response.text.strip():
                        text_response = response.text[:2000]  # 限制长度
                        message = f"📄 <b>文本响应</b>\n\n<pre>{text_response}</pre>"
                        self.send_message(chat_id, message)
                    else:
                        self.send_message(chat_id, "⚠️ 响应为空")
                        
            else:
                # 请求失败
                status_msg += f"❌ 请求失败"
                self.send_message(chat_id, status_msg)
                
                error_msg = f"🔴 <b>错误详情</b>\n"
                error_msg += f"<b>状态码:</b> {response.status_code}\n"
                error_msg += f"<b>状态描述:</b> {response.reason}\n"
                error_msg += f"<b>响应头:</b>\n"
                for key, value in list(response.headers.items())[:5]:
                    error_msg += f"  {key}: {value}\n"
                error_msg += f"\n<b>响应内容:</b>\n<pre>{response.text[:1000]}</pre>"
                self.send_message(chat_id, error_msg)
                
        except requests.exceptions.Timeout:
            timeout_msg = f"⏰ <b>请求超时</b>\n请求超过15秒未响应，可能的原因：\n• 服务器响应慢\n• 网络连接问题\n• 服务器负载高"
            self.send_message(chat_id, timeout_msg)
            
        except requests.exceptions.ConnectionError as conn_err:
            conn_msg = f"🔌 <b>连接错误</b>\n"
            conn_msg += f"<b>错误:</b> {str(conn_err)}\n"
            conn_msg += f"可能的原因：\n• 服务器不可达\n• DNS解析失败\n• 防火墙阻止"
            self.send_message(chat_id, conn_msg)
            
        except requests.exceptions.SSLError as ssl_err:
            ssl_msg = f"🔒 <b>SSL证书错误</b>\n"
            ssl_msg += f"<b>错误:</b> {str(ssl_err)}\n"
            ssl_msg += f"已尝试跳过SSL验证"
            self.send_message(chat_id, ssl_msg)
            
        except requests.exceptions.RequestException as req_err:
            req_msg = f"🔴 <b>请求异常</b>\n"
            req_msg += f"<b>错误类型:</b> {type(req_err).__name__}\n"
            req_msg += f"<b>错误详情:</b> {str(req_err)}"
            self.send_message(chat_id, req_msg)
            
        except Exception as e:
            unexpected_msg = f"💥 <b>未预期错误</b>\n"
            unexpected_msg += f"<b>错误类型:</b> {type(e).__name__}\n"
            unexpected_msg += f"<b>错误详情:</b> {str(e)}"
            self.send_message(chat_id, unexpected_msg)

def format_login_info() -> str:
    """格式化登录信息显示"""
    login_info = {
        "username": "开云",
        "url": "https://www.tjhn65.com:9174",
        "X-API-TOKEN": "DL_dbed342994468370e05e5a535692dfb5",
        "X-API-UUID": "6D80817F-6A14-40CB-98F7-D653B77481D3",
        "X-API-XXX": "c83d342ff52dea61d0c20a42710144a2a8be2af9536255a0fb7809608d8f2a29",
        "Cookie": "X-API-UUID=62a95be6-ae2b-4fa5-916c-027025ca81f0; X-API-TOKEN=DL_dbed342994468370e05e5a535692dfb5"
    }
    
    message = "<b>🔐 登录信息</b>\n\n"
    message += f"<b>用户名:</b> {login_info['username']}\n"
    message += f"<b>登录网站:</b> {login_info['url']}\n\n"
    message += "<b>🔑 API 认证信息:</b>\n"
    message += f"<code>X-API-TOKEN: {login_info['X-API-TOKEN']}</code>\n"
    message += f"<code>X-API-UUID: {login_info['X-API-UUID']}</code>\n"
    message += f"<code>X-API-XXX: {login_info['X-API-XXX']}</code>\n\n"
    message += f"<b>🍪 Cookie:</b>\n<code>{login_info['Cookie']}</code>"
    
    return message

def main():
    # 添加启动标识
    print("=" * 50)
    print("🤖 Telegram Bot 正在启动...")
    print("=" * 50)

    # 打印调试信息
    print(f"当前工作目录: {os.getcwd()}")
    print(f"Python版本: {sys.version}")
    print(f"脚本路径: {os.path.abspath(__file__)}")

    # 强制刷新输出
    sys.stdout.flush()

    # 确保在脚本所在目录运行
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print(f"切换到脚本目录: {script_dir}")

    # 检查必要文件
    token_file = 'bot_token.txt'
    if not os.path.exists(token_file):
        print(f"错误: 在目录 {os.getcwd()} 中找不到 {token_file} 文件")
        print("目录内容:")
        try:
            for item in os.listdir('.'):
                print(f"  - {item}")
        except:
            pass
        return

    # 从文件读取机器人token
    try:
        with open(token_file, 'r', encoding='utf-8') as f:
            BOT_TOKEN = f.read().strip()
        print(f"✅ 成功读取token: {BOT_TOKEN[:10]}...")
        sys.stdout.flush()
    except Exception as e:
        print(f"❌ 错误: 读取token文件失败 - {e}")
        sys.stdout.flush()
        return

    print("🔧 正在初始化TelegramBot实例...")
    sys.stdout.flush()

    try:
        bot = TelegramBot(BOT_TOKEN)
        print("✅ TelegramBot实例创建成功")
        sys.stdout.flush()
    except Exception as e:
        print(f"❌ TelegramBot实例创建失败: {e}")
        sys.stdout.flush()
        return

    print("🚀 机器人启动中...")
    sys.stdout.flush()
    bot.logger.info("机器人启动成功")
    last_update_id = 0
    error_count = 0
    max_errors = 10  # 最大连续错误次数
    last_heartbeat = time.time()
    heartbeat_interval = 300  # 5分钟心跳

    print("🔄 开始消息轮询循环...")
    print("📱 等待Telegram消息...")
    sys.stdout.flush()

    while True:
        try:
            # 获取消息更新
            print(f"🔍 正在获取更新... (offset: {last_update_id + 1})")
            sys.stdout.flush()
            updates = bot.get_updates(offset=last_update_id + 1)

            print(f"📨 收到更新响应: {updates}")
            sys.stdout.flush()

            # 重置错误计数器（成功获取更新）
            error_count = 0

            # 心跳日志
            current_time = time.time()
            if current_time - last_heartbeat > heartbeat_interval:
                bot.logger.info("机器人运行正常 - 心跳检测")
                print(f"心跳: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                last_heartbeat = current_time

            if updates.get("ok") and updates.get("result"):
                print(f"📬 处理 {len(updates['result'])} 条消息")
                sys.stdout.flush()

                for update in updates["result"]:
                    last_update_id = update["update_id"]
                    print(f"🔄 处理更新ID: {last_update_id}")
                    sys.stdout.flush()

                    if "message" in update:
                        message = update["message"]
                        chat_id = message["chat"]["id"]
                        text = message.get("text", "")
                        print(f"💬 收到消息 - Chat ID: {chat_id}, 内容: '{text}'")
                        sys.stdout.flush()
                        
                        # 处理各种命令
                        if text.lower() in ["/start", "/help"]:
                            help_message = """🤖 <b>机器人命令帮助</b>

<b>基本命令:</b>
/data [id] - 获取指定API的会员数据
/summary [id] - 获取综合数据（可选指定ID）
/help - 显示此帮助信息

<b>快速查看命令:</b>
<code>/data 1</code> - 开云会员列表
<code>/data 2</code> - 乐鱼会员列表
<code>/summary</code> - 所有平台综合数据
<code>/summary 1</code> - 开云综合数据

<b>💡 提示:</b>
• 使用 <code>/summary</code> 快速查看所有平台综合数据
• 使用 <code>/summary [1-4]</code> 查看指定平台综合数据
• ID对应：1,3,4=开云 | 2=乐鱼"""
                            bot.send_message(chat_id, help_message)
                            print(f"已向 {chat_id} 发送帮助信息")
                        
                        elif text.lower() in ["/login", "登录信息"]:
                            login_message = format_login_info()
                            bot.send_message(chat_id, login_message)
                            print(f"已向 {chat_id} 发送登录信息")
                        
                        elif text.lower().startswith("/data"):
                            parts = text.split()
                            if len(parts) > 1 and parts[1].isdigit():
                                id = int(parts[1])
                                if 1 <= id <= 4:
                                    bot.fetch_member_data_post_by_id(str(chat_id), id)
                                    print(f"已向 {chat_id} 发送API数据请求 (ID: {id})")
                                else:
                                    bot.send_message(chat_id, "❌ ID范围应为1到4")
                            elif len(parts) > 1:
                                # 处理自定义端点
                                endpoint = parts[1]
                                bot.fetch_api_data_once(str(chat_id), endpoint)
                                print(f"已向 {chat_id} 发送API数据请求 (端点: {endpoint})")
                            else:
                                # 默认获取会员数据 (使用ID=1)
                                bot.fetch_member_data_post_by_id(str(chat_id), 1)
                                print(f"已向 {chat_id} 发送默认API数据请求")
                        
                        elif text.lower() in ["/member", "/members", "会员数据"]:
                            # 使用POST请求获取会员数据 (默认使用ID=1)
                            bot.fetch_member_data_post_by_id(str(chat_id), 1)
                            print(f"已向 {chat_id} 发送POST会员数据请求")

                        elif text.lower().startswith("/summary"):
                            parts = text.split()
                            if len(parts) > 1 and parts[1].isdigit():
                                # 处理 /summary 1, /summary 2 等
                                id = int(parts[1])
                                if 1 <= id <= 4:
                                    bot.fetch_summary_data_by_id(str(chat_id), id)
                                    print(f"已向 {chat_id} 发送综合数据请求 (ID: {id})")
                                else:
                                    bot.send_message(chat_id, "❌ ID范围应为1到4")
                            elif len(parts) == 1 or (len(parts) > 1 and parts[1].lower() == "all"):
                                # 处理 /summary 或 /summary all
                                bot.fetch_all_summary_data(str(chat_id))
                                print(f"已向 {chat_id} 发送所有综合数据请求")
                            else:
                                bot.send_message(chat_id, "❌ 用法: /summary 或 /summary [1-4] 或 /summary all")

                        # 响应其他消息
                        else:
                            if text:  # 只有当有文本内容时才回复
                                response = "👋 你好！发送 /help 查看所有可用命令"
                                bot.send_message(chat_id, response)

            # 检查是否有更新失败的情况
            elif not updates.get("ok"):
                error_msg = updates.get("description", "未知错误")
                print(f"获取更新失败: {error_msg}")
                bot.logger.error(f"获取更新失败: {error_msg}")
                time.sleep(3)  # 等待3秒后重试

            # 短暂休眠避免过度请求
            time.sleep(0.5)

        except requests.exceptions.ConnectionError as e:
            error_count += 1
            print(f"网络连接错误 (第{error_count}次): {e}")
            bot.logger.error(f"网络连接错误 (第{error_count}次): {e}")

            # 网络错误不退出，继续重试
            wait_time = min(30, 5 + error_count * 2)  # 5-30秒等待
            print(f"等待 {wait_time} 秒后重试...")
            time.sleep(wait_time)

        except requests.exceptions.Timeout as e:
            print(f"请求超时: {e}")
            bot.logger.error(f"请求超时: {e}")
            time.sleep(5)  # 超时后等待5秒

        except KeyboardInterrupt:
            print("\n收到中断信号，机器人正在关闭...")
            bot.logger.info("机器人被用户中断")
            break

        except Exception as e:
            error_count += 1
            print(f"未预期错误 (第{error_count}次): {e}")
            bot.logger.error(f"未预期错误 (第{error_count}次): {e}")

            # 即使有错误也不退出，继续运行
            time.sleep(3)  # 等待3秒后重试

            # 如果错误太多，重置计数器
            if error_count >= max_errors:
                print(f"连续{max_errors}次错误，重置错误计数器")
                error_count = 0

if __name__ == "__main__":
    main()
