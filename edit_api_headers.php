<?php
$file = __DIR__ . '/api_headers.json';
$logFile = __DIR__ . '/edit_api_headers.log';

function logMessage($msg) {
    global $logFile;
    $time = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$time] $msg\n", FILE_APPEND);
}

logMessage('Script started');

// 读取API Headers数据
function getApiHeaders() {
    global $file;
    if (!file_exists($file)) {
        return [];
    }
    
    $json = file_get_contents($file);
    $data = json_decode($json, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        logMessage("JSON decode error: " . json_last_error_msg());
        return [];
    }
    
    return is_array($data) ? $data : [];
}

// 渲染主页面
function renderMainPage() {
    $headers = getApiHeaders();
    ob_start();
    ?>
    <!DOCTYPE html>
    <html lang="zh">
    <head>
        <meta charset="UTF-8">
        <title>API Headers 管理</title>
        <style>
            body {
                font-family: sans-serif;
                background: #181a1b;
                color: #e8e6e3;
                margin: 0;
                padding: 20px;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
            }
            h1 {
                text-align: center;
                color: #e8e6e3;
                margin-bottom: 30px;
            }
            .header-card {
                background: #23272f;
                border: 1px solid #333;
                border-radius: 8px;
                margin-bottom: 20px;
                padding: 20px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.5);
            }
            .header-title {
                font-size: 18px;
                font-weight: bold;
                color: #007bff;
                margin-bottom: 15px;
            }
            .field-group {
                display: grid;
                grid-template-columns: 150px 1fr;
                gap: 10px;
                margin-bottom: 10px;
                align-items: center;
            }
            .field-label {
                font-weight: bold;
                color: #ccc;
            }
            .field-value {
                background: #181a1b;
                padding: 8px;
                border-radius: 4px;
                border: 1px solid #444;
                word-break: break-all;
            }
            .edit-btn {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                cursor: pointer;
                text-decoration: none;
                display: inline-block;
                margin-top: 10px;
            }
            .edit-btn:hover {
                background-color: #218838;
            }
            .add-btn {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 4px;
                cursor: pointer;
                text-decoration: none;
                display: inline-block;
                margin-bottom: 20px;
                font-size: 16px;
            }
            .add-btn:hover {
                background-color: #0056b3;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>API Headers 管理</h1>
            
            <a href="?action=add" class="add-btn">添加新的 API Header</a>
            
            <?php if (empty($headers)): ?>
                <p style="text-align: center; color: #888;">暂无API Headers数据</p>
            <?php else: ?>
                <?php foreach ($headers as $header): ?>
                    <div class="header-card">
                        <div class="header-title">API Header #<?php echo htmlspecialchars($header['id'] ?? ''); ?></div>
                        
                        <div class="field-group">
                            <div class="field-label">ID:</div>
                            <div class="field-value"><?php echo htmlspecialchars($header['id'] ?? ''); ?></div>
                        </div>
                        
                        <div class="field-group">
                            <div class="field-label">Username:</div>
                            <div class="field-value"><?php echo htmlspecialchars($header['username'] ?? ''); ?></div>
                        </div>
                        
                        <div class="field-group">
                            <div class="field-label">URL:</div>
                            <div class="field-value"><?php echo htmlspecialchars($header['url'] ?? ''); ?></div>
                        </div>
                        
                        <div class="field-group">
                            <div class="field-label">X-API-TOKEN:</div>
                            <div class="field-value"><?php echo htmlspecialchars($header['X-API-TOKEN'] ?? ''); ?></div>
                        </div>
                        
                        <div class="field-group">
                            <div class="field-label">X-API-UUID:</div>
                            <div class="field-value"><?php echo htmlspecialchars($header['X-API-UUID'] ?? ''); ?></div>
                        </div>
                        
                        <div class="field-group">
                            <div class="field-label">X-API-XXX:</div>
                            <div class="field-value"><?php echo htmlspecialchars($header['X-API-XXX'] ?? ''); ?></div>
                        </div>
                        
                        <div class="field-group">
                            <div class="field-label">Cookie:</div>
                            <div class="field-value"><?php echo htmlspecialchars($header['Cookie'] ?? ''); ?></div>
                        </div>
                        
                        <a href="?action=edit&id=<?php echo urlencode($header['id'] ?? ''); ?>" class="edit-btn">编辑</a>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </body>
    </html>
    <?php
    return ob_get_clean();
}

// 渲染编辑表单
function renderForm($editData = null) {
    $isEdit = !empty($editData);
    $title = $isEdit ? '编辑 API Header' : '添加 API Header';
    
    ob_start();
    ?>
    <!DOCTYPE html>
    <html lang="zh">
    <head>
        <meta charset="UTF-8">
        <title><?php echo $title; ?></title>
        <style>
            body {
                font-family: sans-serif;
                background: #181a1b;
                color: #e8e6e3;
            }
            .container {
                max-width: 600px;
                margin: 2em auto;
                padding: 20px;
            }
            form {
                padding: 2em;
                border: 1px solid #333;
                border-radius: 8px;
                background: #23272f;
                box-shadow: 0 2px 8px rgba(0,0,0,0.5);
            }
            h1 {
                text-align: center;
                margin-bottom: 30px;
                color: #e8e6e3;
            }
            label {
                display: block;
                margin-bottom: 0.5em;
                color: #e8e6e3;
                font-weight: bold;
            }
            input {
                width: 100%;
                padding: 0.8em;
                margin-bottom: 1.5em;
                box-sizing: border-box;
                background: #181a1b;
                color: #e8e6e3;
                border: 1px solid #444;
                border-radius: 4px;
                font-size: 14px;
            }
            input:focus {
                outline: none;
                border-color: #007bff;
                background: #23272f;
            }
            .button-group {
                display: flex;
                gap: 10px;
                justify-content: center;
                margin-top: 20px;
            }
            button {
                padding: 0.8em 2em;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 16px;
                text-decoration: none;
                display: inline-block;
            }
            .save-btn {
                background-color: #28a745;
                color: #fff;
            }
            .save-btn:hover {
                background-color: #218838;
            }
            .cancel-btn {
                background-color: #6c757d;
                color: #fff;
            }
            .cancel-btn:hover {
                background-color: #5a6268;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <form method="post">
                <h1><?php echo $title; ?></h1>
                
                <label for="id">ID</label>
                <input type="number" id="id" name="header[id]" min="1" required 
                       value="<?php echo htmlspecialchars($editData['id'] ?? ''); ?>"
                       <?php echo $isEdit ? 'readonly' : ''; ?>>

                <label for="username">Username</label>
                <input type="text" id="username" name="header[username]" required 
                       value="<?php echo htmlspecialchars($editData['username'] ?? ''); ?>">

                <label for="url">URL</label>
                <input type="text" id="url" name="header[url]" required 
                       value="<?php echo htmlspecialchars($editData['url'] ?? ''); ?>">

                <label for="X-API-TOKEN">X-API-TOKEN</label>
                <input type="text" id="X-API-TOKEN" name="header[X-API-TOKEN]" required 
                       value="<?php echo htmlspecialchars($editData['X-API-TOKEN'] ?? ''); ?>">

                <label for="X-API-UUID">X-API-UUID</label>
                <input type="text" id="X-API-UUID" name="header[X-API-UUID]" required 
                       value="<?php echo htmlspecialchars($editData['X-API-UUID'] ?? ''); ?>">

                <label for="X-API-XXX">X-API-XXX</label>
                <input type="text" id="X-API-XXX" name="header[X-API-XXX]" required 
                       value="<?php echo htmlspecialchars($editData['X-API-XXX'] ?? ''); ?>">

                <label for="Cookie">Cookie</label>
                <input type="text" id="Cookie" name="header[Cookie]" required 
                       value="<?php echo htmlspecialchars($editData['Cookie'] ?? ''); ?>">

                <div class="button-group">
                    <button type="submit" class="save-btn">保存</button>
                    <a href="?" class="cancel-btn" style="line-height: 1.5;">取消</a>
                </div>
            </form>
        </div>
    </body>
    </html>
    <?php
    return ob_get_clean();
}

// 保存API Headers数据
function saveApiHeaders($data) {
    global $file;
    $result = file_put_contents($file, json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    if ($result === false) {
        logMessage("Failed to write to file");
        return false;
    } else {
        logMessage("Successfully wrote " . $result . " bytes to file");
        return true;
    }
}

// 根据ID查找API Header
function findHeaderById($id) {
    $headers = getApiHeaders();
    foreach ($headers as $header) {
        if (isset($header['id']) && strval($header['id']) === strval($id)) {
            return $header;
        }
    }
    return null;
}

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    logMessage("POST request received");
    $newHeader = $_POST['header'];
    
    // 确保所有字段都存在
    $requiredFields = ['id', 'username', 'url', 'X-API-TOKEN', 'X-API-UUID', 'X-API-XXX', 'Cookie'];
    foreach ($requiredFields as $field) {
        if (!isset($newHeader[$field]) || trim($newHeader[$field]) === '') {
            logMessage("Missing required field: " . $field);
            header("Location: " . $_SERVER['PHP_SELF'] . "?error=missing_field");
            exit;
        }
    }

    $data = getApiHeaders();
    
    // 查找是否存在相同ID的记录
    $found = false;
    if (is_array($data)) {
        foreach ($data as $idx => $item) {
            if (isset($item['id']) && strval($item['id']) === strval($newHeader['id'])) {
                $data[$idx] = $newHeader;
                $found = true;
                logMessage("Updated existing header with ID: " . $newHeader['id']);
                break;
            }
        }
    }
    
    // 如果没找到，添加新记录
    if (!$found) {
        $data[] = $newHeader;
        logMessage("Added new header with ID: " . $newHeader['id']);
    }
    
    // 保存数据
    if (saveApiHeaders($data)) {
        header("Location: " . $_SERVER['PHP_SELF'] . "?success=1");
    } else {
        header("Location: " . $_SERVER['PHP_SELF'] . "?error=save_failed");
    }
    exit;
}

// 处理GET请求
$action = $_GET['action'] ?? '';
$id = $_GET['id'] ?? '';

switch ($action) {
    case 'add':
        logMessage("Rendering add form");
        echo renderForm();
        break;
        
    case 'edit':
        if (empty($id)) {
            logMessage("Edit action without ID, redirecting to main page");
            header("Location: " . $_SERVER['PHP_SELF']);
            exit;
        }
        
        $editData = findHeaderById($id);
        if (!$editData) {
            logMessage("Header with ID $id not found, redirecting to main page");
            header("Location: " . $_SERVER['PHP_SELF'] . "?error=not_found");
            exit;
        }
        
        logMessage("Rendering edit form for ID: $id");
        echo renderForm($editData);
        break;
        
    default:
        logMessage("Rendering main page");
        echo renderMainPage();
        break;
}
?>
